#!/usr/bin/env python3
"""
搜狗输入法
支持平台: Ubuntu/Debian
架构: x86_64/arm64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

from packages.common.package_downloader import download_package

def check_sudo_nopasswd():
    """检查sudo无密码权限"""
    try:
        result = subprocess.run(["sudo", "-n", "true"],
                              capture_output=True,
                              timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
        return False

def get_latest_version():
    return "*********"

def get_architecture():
    try:
        result = subprocess.run("uname -m", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            arch = result.stdout.strip()
            if arch == "x86_64":
                return "amd64"
            elif arch == "aarch64":
                return "arm64"
    except:
        pass
    return "amd64"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    arch = get_architecture()
    filename = f"sogoupinyin_{version}_{arch}.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载搜狗输入法...")

        if arch == "amd64":
            download_url = "https://ime-sec.gtimg.com/202508021111/8fc8f6fe4e8db0c4c235d784a8b09833/pc/dl/gzindex/1680521603/sogoupinyin_*********_amd64.deb"
        else:
            download_url = "https://ime-sec.gtimg.com/202508021111/6a4ba299facda8826ced5b6813fe717a/pc/dl/gzindex/1680521473/sogoupinyin_*********_arm64.deb"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    # 检查sudo无密码权限
    if not check_sudo_nopasswd():
        print("❌ 需要sudo无密码权限才能安装")
        print("💡 请运行主安装程序或手动配置sudo无密码权限")
        print("   sudo visudo")
        print("   添加: $(whoami) ALL=(ALL) NOPASSWD: ALL")
        return False

    status_code = check_status()
    if status_code != 2:
        print("🔄 检测到已安装版本，先进行卸载...")
        uninstall()

    downloaded_file, _ = download_package(
        package_name="sogou",
        online_downloader=download,
        package_type="deb"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    print("🔄 开始安装搜狗输入法...")

    run_command_sync("sudo apt update", "更新软件包列表", capture_output=True)

    print("🔄 卸载可能冲突的ibus输入法框架...")
    run_command_sync("sudo apt purge -y ibus", "卸载ibus输入法框架", capture_output=True)

    print("🔄 安装fcitx输入法框架及相关组件...")
    run_command_sync("sudo apt install -y fcitx fcitx-config-gtk fcitx-table-all fcitx-frontend-all fcitx-module-cloudpinyin fcitx-googlepinyin", "安装fcitx完整组件")

    print("🔄 安装搜狗输入法依赖...")
    run_command_sync("sudo apt install -y libqt5qml5 libqt5quick5 libqt5quickwidgets5 qml-module-qtquick2 libgsettings-qt1", "安装输入法依赖")

    print("🔄 安装搜狗输入法...")
    run_command_sync(f"sudo dpkg -i '{downloaded_file}'", "安装搜狗输入法")
    run_command_sync("sudo apt install -f -y", "修复依赖关系", capture_output=True)

    print("🔄 配置fcitx自启动...")
    run_command_sync("sudo cp /usr/share/applications/fcitx.desktop /etc/xdg/autostart/", "设置fcitx开机自启动", capture_output=True)

    _, user_home, uid, gid = get_user_info()

    print("🔄 配置用户环境...")
    config_dir = os.path.join(user_home, ".config")
    os.makedirs(config_dir, exist_ok=True)

    profile_content = '''
# fcitx输入法环境变量
export GTK_IM_MODULE=fcitx
export QT_IM_MODULE=fcitx
export XMODIFIERS=@im=fcitx
'''

    profile_file = os.path.join(user_home, ".profile")
    try:
        with open(profile_file, 'a') as f:
            f.write(profile_content)
        os.chown(profile_file, uid, gid)
        print("✅ 已配置用户环境变量")
    except Exception as e:
        print(f"⚠️ 配置环境变量失败: {e}")

    try:
        os.chown(config_dir, uid, gid)
    except PermissionError:
        pass

    print("✅ 搜狗输入法安装完成")
    print("💡 安装后操作指南:")
    print("   1. 注销并重新登录系统")
    print("   2. 在系统设置 -> 区域和语言 -> 管理已安装的语言中")
    print("   3. 将键盘输入法系统设置为 fcitx")
    print("   4. 重启系统后即可使用搜狗输入法")
    return True

def uninstall():
    print("🔄 卸载搜狗输入法...")

    # 停止进程（避免杀死自己）
    try:
        result = subprocess.run("pgrep -f sogoupinyin", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            for pid in result.stdout.strip().split('\n'):
                if pid and pid.strip():
                    subprocess.run(f"kill {pid.strip()}", shell=True, capture_output=True)
    except:
        pass

    # 卸载软件包
    run_command_sync("sudo apt remove --purge -y sogoupinyin", "卸载搜狗输入法", capture_output=True)
    run_command_sync("sudo snap remove sogou-pinyin", "卸载Snap版本", capture_output=True)

    # 清理配置文件
    _, user_home, _, _ = get_user_info()
    config_dirs = [
        os.path.join(user_home, ".config/sogou-qimpanel"),
        os.path.join(user_home, ".config/SogouPY"),
        os.path.join(user_home, ".sogouinput"),
        os.path.join(user_home, ".cache/sogou-qimpanel")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            shutil.rmtree(config_dir, ignore_errors=True)

    print("✅ 卸载完成")
    return True

def check_status():
    result = run_command_sync("dpkg -l | grep sogoupinyin", capture_output=True, silent=True)
    if result.returncode == 0 and result.stdout.strip():
        print('🟢 状态: 已安装')
        return 0

    result = run_command_sync("snap list sogou-pinyin", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
