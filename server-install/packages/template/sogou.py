#!/usr/bin/env python3
"""
搜狗输入法
支持平台: Ubuntu/Debian
架构: x86_64/arm64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

from packages.common.package_downloader import download_package

def get_latest_version():
    return "*********"

def get_architecture():
    try:
        result = subprocess.run("uname -m", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            arch = result.stdout.strip()
            if arch == "x86_64":
                return "amd64"
            elif arch == "aarch64":
                return "arm64"
    except:
        pass
    return "amd64"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    arch = get_architecture()
    filename = f"sogoupinyin_{version}_{arch}.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载搜狗输入法...")

        if arch == "amd64":
            download_url = "https://ime-sec.gtimg.com/202508021111/8fc8f6fe4e8db0c4c235d784a8b09833/pc/dl/gzindex/1680521603/sogoupinyin_*********_amd64.deb"
        else:
            download_url = "https://ime-sec.gtimg.com/202508021111/6a4ba299facda8826ced5b6813fe717a/pc/dl/gzindex/1680521473/sogoupinyin_*********_arm64.deb"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="sogou",
        online_downloader=download,
        package_type="deb"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    print("🔄 开始安装搜狗输入法...")

    run_command_sync("sudo apt update", "更新软件包列表", capture_output=True)

    print("🔄 卸载可能冲突的ibus输入法框架...")
    run_command_sync("sudo apt purge -y ibus", "卸载ibus输入法框架", capture_output=True)

    print("🔄 安装fcitx输入法框架及相关组件...")
    run_command_sync("sudo apt install -y fcitx fcitx-config-gtk fcitx-table-all fcitx-frontend-all fcitx-module-cloudpinyin fcitx-googlepinyin", "安装fcitx完整组件")

    print("🔄 安装搜狗输入法依赖...")
    run_command_sync("sudo apt install -y libqt5qml5 libqt5quick5 libqt5quickwidgets5 qml-module-qtquick2 libgsettings-qt1", "安装输入法依赖")

    print("🔄 安装搜狗输入法...")
    run_command_sync(f"sudo dpkg -i '{downloaded_file}'", "安装搜狗输入法")
    run_command_sync("sudo apt install -f -y", "修复依赖关系", capture_output=True)

    print("🔄 配置fcitx自启动...")
    run_command_sync("sudo cp /usr/share/applications/fcitx.desktop /etc/xdg/autostart/", "设置fcitx开机自启动", capture_output=True)

    _, user_home, uid, gid = get_user_info()

    print("🔄 配置用户环境...")
    config_dir = os.path.join(user_home, ".config")
    os.makedirs(config_dir, exist_ok=True)

    profile_content = '''
# fcitx输入法环境变量
export GTK_IM_MODULE=fcitx
export QT_IM_MODULE=fcitx
export XMODIFIERS=@im=fcitx
'''

    profile_file = os.path.join(user_home, ".profile")
    try:
        with open(profile_file, 'a') as f:
            f.write(profile_content)
        os.chown(profile_file, uid, gid)
        print("✅ 已配置用户环境变量")
    except Exception as e:
        print(f"⚠️ 配置环境变量失败: {e}")

    try:
        os.chown(config_dir, uid, gid)
    except PermissionError:
        pass

    print("✅ 搜狗输入法安装完成")
    print("💡 安装后操作指南:")
    print("   1. 注销并重新登录系统")
    print("   2. 在系统设置 -> 区域和语言 -> 管理已安装的语言中")
    print("   3. 将键盘输入法系统设置为 fcitx")
    print("   4. 重启系统后即可使用搜狗输入法")
    return True

def uninstall():
    print("🔄 开始卸载搜狗输入法...")

    print("🔄 停止相关进程...")
    result = run_command_sync("pgrep -f sogou", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    run_command_sync(f"kill {pid.strip()}", f"停止进程 {pid.strip()}", capture_output=True)
                except:
                    continue

    result = run_command_sync("pgrep -f fcitx", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    run_command_sync(f"kill {pid.strip()}", f"停止fcitx进程 {pid.strip()}", capture_output=True)
                except:
                    continue

    print("🔄 卸载软件包...")
    run_command_sync("sudo snap remove sogou-pinyin", "检查并卸载Snap版本", capture_output=True)

    run_command_sync("sudo apt remove --purge -y sogoupinyin", "卸载搜狗输入法", capture_output=True)
    run_command_sync("sudo apt remove --purge -y fcitx fcitx-*", "卸载fcitx输入法框架", capture_output=True)
    run_command_sync("sudo apt autoremove -y", "清理依赖包", capture_output=True)

    print("🔄 清理系统配置...")
    run_command_sync("sudo rm -f /etc/xdg/autostart/fcitx.desktop", "清理fcitx自启动配置", capture_output=True)

    _, user_home, _, _ = get_user_info()

    print("🔄 清理用户配置...")
    config_dirs = [
        os.path.join(user_home, ".config/sogou-qimpanel"),
        os.path.join(user_home, ".config/SogouPY"),
        os.path.join(user_home, ".config/fcitx"),
        os.path.join(user_home, ".sogouinput"),
        os.path.join(user_home, ".cache/sogou-qimpanel"),
        os.path.join(user_home, ".cache/fcitx")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            run_command_sync(f"rm -rf '{config_dir}'", f"清理配置目录 {os.path.basename(config_dir)}", capture_output=True)

    print("🔄 清理环境变量...")
    profile_file = os.path.join(user_home, ".profile")
    if os.path.exists(profile_file):
        try:
            with open(profile_file, 'r') as f:
                lines = f.readlines()

            new_lines = []
            skip_next = False
            for line in lines:
                if "# fcitx输入法环境变量" in line:
                    skip_next = True
                    continue
                if skip_next and ("GTK_IM_MODULE" in line or "QT_IM_MODULE" in line or "XMODIFIERS" in line):
                    continue
                else:
                    skip_next = False
                    new_lines.append(line)

            with open(profile_file, 'w') as f:
                f.writelines(new_lines)
            print("✅ 已清理环境变量配置")
        except Exception as e:
            print(f"⚠️ 清理环境变量失败: {e}")

    print("✅ 卸载完成")
    print("💡 建议重启系统以完全清理输入法配置")
    return True

def check_status():
    result = run_command_sync("dpkg -l | grep sogoupinyin", capture_output=True, silent=True)
    if result.returncode == 0 and result.stdout.strip():
        print('🟢 状态: 已安装')
        return 0

    result = run_command_sync("snap list sogou-pinyin", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
